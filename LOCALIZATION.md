# Localization System

This document describes the localization system implemented for the Telegram bot.

## Overview

The localization system allows you to:
- Store all text messages in a centralized location
- Support multiple languages (currently English and Russian)
- Configure a global default language
- Easily add new text or languages
- Format text with dynamic values

## Configuration

### Setting Default Language

In `models/config.py`, set the `DEFAULT_LANGUAGE` variable:

```python
DEFAULT_LANGUAGE = "ru"  # For Russian (default)
DEFAULT_LANGUAGE = "en"  # For English
```

## Usage

### Basic Usage

```python
from models.localization import localization, Language

# Get text in default language
message = localization.get_text("rate_limit_exceeded")

# Get text in specific language
message = localization.get_text("rate_limit_exceeded", Language.ENGLISH)

# Get text with formatting
greeting = localization.get_text("start_greeting", user="@username")
```

### Available Text Keys

Current available text keys:
- `rate_limit_exceeded` - Rate limiting message
- `no_response` - Error when AI doesn't respond
- `unexpected_error` - General error message
- `start_greeting` - Welcome message (requires `user` parameter)
- `help_text` - Help command text
- `context_cleared` - Context reset success message
- `context_clear_failed` - Context reset failure message
- `current_model` - Model info (requires `model_name` parameter)

### Adding New Text

```python
# Add new text with translations
localization.add_text("new_feature", {
    "en": "New feature is now available!",
    "ru": "Новая функция теперь доступна!"
})

# Use the new text
message = localization.get_text("new_feature")
```

### Changing Language at Runtime

```python
# Change default language
localization.set_default_language(Language.ENGLISH)

# All subsequent calls will use English by default
message = localization.get_text("help_text")
```

## File Structure

```
models/
├── localization.py     # Main localization class
└── config.py          # Configuration including DEFAULT_LANGUAGE

examples/
└── localization_example.py  # Usage examples
```

## Adding New Languages

To add a new language:

1. Add the language to the `Language` enum in `models/localization.py`:
```python
class Language(Enum):
    ENGLISH = "en"
    RUSSIAN = "ru"
    SPANISH = "es"  # New language
```

2. Add translations for all existing text keys:
```python
"rate_limit_exceeded": {
    "en": "⚠️ You're sending messages too fast. Please wait a moment.",
    "ru": "⚠️ Вы отправляете сообщения слишком быстро. Пожалуйста, подождите немного.",
    "es": "⚠️ Estás enviando mensajes muy rápido. Por favor espera un momento."
},
```

3. Update the config to use the new language:
```python
DEFAULT_LANGUAGE = "es"
```

## Telegram Markdown Formatting

The system handles Telegram's MarkdownV2 formatting automatically. Special characters are escaped with backslashes (`\`) where needed.

## Error Handling

The localization system includes fallback mechanisms:
- If a text key doesn't exist, returns `[Missing text: key]`
- If a language isn't available, falls back to English
- If English isn't available, returns `[Missing translation: key]`
- If formatting fails, returns `[Format error in key: missing parameter]`

## Migration from Old System

The old hardcoded strings have been replaced with localization calls:

**Before:**
```python
too_fast_ru = "⚠️ Вы отправляете сообщения слишком быстро. Пожалуйста, подождите немного."
await update.message.reply_text(too_fast_ru)
```

**After:**
```python
await update.message.reply_text(localization.get_text("rate_limit_exceeded"))
```

## Example Script

Run the example script to see the localization system in action:

```bash
python examples/localization_example.py
```

This will demonstrate:
- Getting text in different languages
- Adding new text
- Changing the default language
- Text formatting with parameters
