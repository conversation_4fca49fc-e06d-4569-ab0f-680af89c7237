"""
Example of how to use the localization system.
This demonstrates how to add new text, change languages, and extend the system.
"""

import sys
import os

# Add the parent directory to the path so we can import from models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.localization import localization, Language


def main():
    print("=== Localization System Example ===\n")
    
    # Show current default language
    print(f"Current default language: {localization.default_language.value}")
    
    # Get some existing text in default language (Russian)
    print(f"Rate limit message (RU): {localization.get_text('rate_limit_exceeded')}")
    
    # Get the same text in English
    print(f"Rate limit message (EN): {localization.get_text('rate_limit_exceeded', Language.ENGLISH)}")
    
    print("\n" + "="*50 + "\n")
    
    # Add new custom text
    localization.add_text("welcome_back", {
        "en": "Welcome back! How can I help you today?",
        "ru": "С возвращением! Как я могу помочь вам сегодня?"
    })
    
    print("Added new text 'welcome_back':")
    print(f"English: {localization.get_text('welcome_back', Language.ENGLISH)}")
    print(f"Russian: {localization.get_text('welcome_back', Language.RUSSIAN)}")
    
    print("\n" + "="*50 + "\n")
    
    # Change default language to English
    localization.set_default_language(Language.ENGLISH)
    print(f"Changed default language to: {localization.default_language.value}")
    
    # Now get text without specifying language (should be English)
    print(f"Rate limit message (default): {localization.get_text('rate_limit_exceeded')}")
    print(f"Welcome back (default): {localization.get_text('welcome_back')}")
    
    print("\n" + "="*50 + "\n")
    
    # Show all available text keys
    print("All available text keys:")
    for key in localization.get_all_keys():
        print(f"  - {key}")
    
    print("\n" + "="*50 + "\n")
    
    # Demonstrate text formatting
    print("Text formatting example:")
    start_text = localization.get_text("start_greeting", user="@TestUser")
    print(f"Start greeting: {start_text}")
    
    model_text = localization.get_text("current_model", model_name="gpt-4")
    print(f"Model info: {model_text}")


if __name__ == "__main__":
    main()
